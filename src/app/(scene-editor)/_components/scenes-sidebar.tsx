'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog'
import { List, Settings, Music2, Pause, Play, Volume2, Mic } from 'lucide-react'
import { useVideoStore } from '@/store/video-store'
import { useGatedVoiceGeneration } from '@/hooks/use-gated-voice-generation'
import { cn } from '@/lib/utils'
import { useState, useRef, useEffect } from 'react'
import { MediaPickerModal, Media as PickerMedia } from './media-picker-modal'
import { VoicePickerModal } from './voice-picker-modal'
import { MusicPickerModal } from './music-picker-modal'
import type { Media as VideoMedia } from '@/types/video'
import type { ElevenVoice } from '@/hooks/useElevenVoicesQuery'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import { Slider } from '@/components/ui/slider'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { VisuallyHidden } from '@/components/ui/visually-hidden'
import { authClient } from '@/lib/auth-client'
import { toast } from 'sonner'

// Import reusable components
import {
  AIAssistant,
  SceneMediaThumbnail,
  SceneControls,
  SceneEffects,
  SceneHeader,
  SceneActions,
} from './scene-components'
import { CaptionSettings } from './settings-components'
import { useAIAssistant } from './hooks/use-ai-assistant'
import { useVoiceRegeneration } from '@/hooks/use-feature-gating'
import {
  useGlobalAudio,
  useAudioCleanup,
} from '@/lib/audio/global-audio-manager'

export function ScenesSidebar({ projectId }: { projectId: string | null }) {
  const {
    scenes,
    currentScene,
    setCurrentScene,
    addScene,
    deleteScene,
    updateScene,
    selectedMusic,
    setSelectedMusic,
    musicVolume,
    setMusicVolume,
    musicEnabled,
    setMusicEnabled,
    project,
    seekToTime,
  } = useVideoStore()

  const { data: session } = authClient.useSession()

  // --- Speech-specific state ---
  const [selectedSpeech] = useState(() => project?.speech || null)
  const [speechVolume, setSpeechVolume] = useState(
    () => project?.speech?.volume ?? 100
  )
  const speechAudioRef = useRef<HTMLAudioElement | null>(null)

  // Gated voice generation with project ID
  const gatedVoiceGeneration = useGatedVoiceGeneration(projectId || '')

  // Voice regeneration tracking for limit checking
  const voiceRegeneration = useVoiceRegeneration(projectId || '')

  const isMobile =
    typeof window !== 'undefined'
      ? window.matchMedia('(max-width: 767px)').matches
      : false

  // Modal state
  const [mediaPickerSceneId, setMediaPickerSceneId] = useState<string | null>(
    null
  )
  const [voicePickerSceneId, setVoicePickerSceneId] = useState<string | null>(
    null
  )
  const [musicPickerOpen, setMusicPickerOpen] = useState(false)
  const [previewMedia, setPreviewMedia] = useState<{
    url: string
    type: string
  } | null>(null)

  // Toggle state for Scenes/Setting
  const [tab, setTab] = useState<'scenes' | 'settings'>('scenes')

  // Audio playback state for voiceover controls
  const [isPlaying, setIsPlaying] = useState<{ [key: string]: boolean }>({})
  const audioRefs = useRef<{ [key: string]: HTMLAudioElement | null }>({})

  // Per-scene voice generation state
  const [isGenerating, setIsGenerating] = useState<{ [key: string]: boolean }>(
    {}
  )

  // Background music playback state
  const [isMusicPlaying, setIsMusicPlaying] = useState(false)
  const musicGlobalAudio = useGlobalAudio('scenes-sidebar-music')

  // Cleanup music audio when component unmounts
  useAudioCleanup('scenes-sidebar-music')

  // Per-scene input state (moved outside map function)
  const [sceneInputs, setSceneInputs] = useState<{
    [sceneId: string]: {
      startInput: string
      endInput: string
      endError: string | null
      showEndError: boolean
    }
  }>({})

  // Initialize scene inputs when scenes change
  useEffect(() => {
    const newInputs: typeof sceneInputs = {}
    scenes.forEach(scene => {
      newInputs[scene.id] = {
        startInput: secondsToMmssSS(scene.startOffset || 0),
        endInput: secondsToMmssSS(
          (scene.startOffset || 0) + (scene.duration || 0)
        ),
        endError: null,
        showEndError: false,
      }
    })
    setSceneInputs(newInputs)
  }, [scenes])

  // Update scene inputs when scene data changes
  useEffect(() => {
    setSceneInputs(prev => {
      const updated = { ...prev }
      scenes.forEach(scene => {
        if (updated[scene.id]) {
          updated[scene.id] = {
            ...updated[scene.id],
            startInput: secondsToMmssSS(scene.startOffset || 0),
            endInput: secondsToMmssSS(
              (scene.startOffset || 0) + (scene.duration || 0)
            ),
          }
        }
      })
      return updated
    })
  }, [scenes])

  // Helper function to update scene input
  const updateSceneInput = (
    sceneId: string,
    updates: Partial<(typeof sceneInputs)[string]>
  ) => {
    setSceneInputs(prev => ({
      ...prev,
      [sceneId]: { ...prev[sceneId], ...updates },
    }))
  }

  // AI Assistant hook
  const aiAssistant = useAIAssistant(updateScene)

  // Remove the old generateSpeech - now using gated version

  const isPodcastOrAudioFlow = !!project?.speech

  const handleSceneSelect = (sceneId: string) => {
    setCurrentScene(sceneId)

    // Seek to the scene's start time in the video player
    const scene = scenes.find(s => s.id === sceneId)
    if (scene && isPodcastOrAudioFlow) {
      // For podcast/audio flow, seek to the scene's startOffset
      seekToTime(scene.startOffset || 0)
    } else if (scene) {
      // For regular flow, calculate cumulative time up to this scene
      let cumulativeTime = 0
      for (let i = 0; i < scenes.length; i++) {
        if (scenes[i].id === sceneId) {
          break
        }
        cumulativeTime += scenes[i].duration || 0
      }
      seekToTime(cumulativeTime)
    }
  }

  const handleAddScene = (afterIndex: number) => {
    addScene(afterIndex + 1)
  }

  const handleDeleteScene = (sceneId: string) => {
    deleteScene(sceneId)
  }

  // Move scene up
  const handleMoveUp = (index: number) => {
    if (index <= 0) return
    const isPodcastOrAudioFlow = !!project?.speech
    const newScenes = [...scenes]
    if (isPodcastOrAudioFlow) {
      // Swap timing fields for podcast flow
      const a = newScenes[index - 1]
      const b = newScenes[index]
      const tempStart = a.startOffset
      const tempDuration = a.duration
      a.startOffset = b.startOffset
      a.duration = b.duration
      b.startOffset = tempStart
      b.duration = tempDuration
    }
    const temp = newScenes[index - 1]
    newScenes[index - 1] = newScenes[index]
    newScenes[index] = temp
    newScenes.forEach((scene, idx) => {
      scene.name = `Scene ${idx + 1}`
    })
    updateScene(newScenes[index].id, {})
    useVideoStore.setState({ scenes: newScenes })
  }

  // Move scene down
  const handleMoveDown = (index: number) => {
    if (index >= scenes.length - 1) return
    const isPodcastOrAudioFlow = !!project?.speech
    const newScenes = [...scenes]
    if (isPodcastOrAudioFlow) {
      // Swap timing fields for podcast flow
      const a = newScenes[index]
      const b = newScenes[index + 1]
      const tempStart = a.startOffset
      const tempDuration = a.duration
      a.startOffset = b.startOffset
      a.duration = b.duration
      b.startOffset = tempStart
      b.duration = tempDuration
    }
    const temp = newScenes[index + 1]
    newScenes[index + 1] = newScenes[index]
    newScenes[index] = temp
    newScenes.forEach((scene, idx) => {
      scene.name = `Scene ${idx + 1}`
    })
    updateScene(newScenes[index].id, {})
    useVideoStore.setState({ scenes: newScenes })
  }

  const formatDuration = (duration: number) => {
    const mins = Math.floor(duration / 60)
    const secs = Math.floor(duration % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // Handlers for modals
  const handleMediaPicker = (sceneId: string) => {
    setMediaPickerSceneId(sceneId)
  }
  const handleVoicePicker = (sceneId: string) => {
    setVoicePickerSceneId(sceneId)
  }

  // Convert media picker Media to video store Media format
  // const convertPickerMediaToVideoMedia = (
  //   pickerMedia: PickerMedia
  // ): VideoMedia => {
  //   const mediaUrl =
  //     pickerMedia.type === 'video'
  //       ? pickerMedia.videoUrl ||
  //         pickerMedia.url ||
  //         pickerMedia.src ||
  //         pickerMedia.thumbnail
  //       : pickerMedia.url || pickerMedia.src || pickerMedia.thumbnail

  //   return {
  //     id: pickerMedia.id.toString(),
  //     type: pickerMedia.type === 'video' ? 'video' : 'image',
  //     url: mediaUrl,
  //     position: { x: 0, y: 0 },
  //     size: {
  //       width: pickerMedia.width || 1920,
  //       height: pickerMedia.height || 1080,
  //     },
  //     startTime: 0,
  //     endTime: 5,
  //     thumbnail: pickerMedia.thumbnail,
  //     duration: pickerMedia.duration,
  //   }
  // }

  // Update existing media with new asset while preserving scene settings
  const updateMediaWithNewAsset = (
    existingMedia: VideoMedia | null | undefined,
    pickerMedia: PickerMedia
  ): VideoMedia => {
    const mediaUrl =
      pickerMedia.type === 'video'
        ? pickerMedia.videoUrl ||
          pickerMedia.url ||
          pickerMedia.src ||
          pickerMedia.thumbnail
        : pickerMedia.url || pickerMedia.src || pickerMedia.thumbnail

    // If no existing media, create new one with default settings
    if (!existingMedia) {
      return {
        id: pickerMedia.id.toString(),
        type: pickerMedia.type === 'video' ? 'video' : 'image',
        url: mediaUrl,
        position: { x: 0, y: 0 },
        size: {
          width: pickerMedia.width || 1920,
          height: pickerMedia.height || 1080,
        },
        startTime: 0,
        endTime: 5,
        thumbnail: pickerMedia.thumbnail,
        duration: pickerMedia.duration,
        // Default settings for new media
        fit: 'blur',
        transition: 'fade',
        kenBurns: 'zoom-in',
        effectDuration: 1.5,
      }
    }

    // Preserve existing settings, only update the asset-specific properties
    return {
      ...existingMedia,
      id: pickerMedia.id.toString(),
      type: pickerMedia.type === 'video' ? 'video' : 'image',
      url: mediaUrl,
      size: {
        width: pickerMedia.width || existingMedia.size.width,
        height: pickerMedia.height || existingMedia.size.height,
      },
      thumbnail: pickerMedia.thumbnail,
      duration: pickerMedia.duration,
      // Keep all existing scene-specific settings:
      // fit, transition, kenBurns, effectDuration, position, startTime, endTime
    }
  }

  // Handle media select
  const handleMediaSelect = (media: PickerMedia, applyToAll: boolean) => {
    if (!mediaPickerSceneId) return

    if (applyToAll) {
      scenes.forEach(scene => {
        const updatedMedia = updateMediaWithNewAsset(scene.media, media)
        updateScene(scene.id, { media: updatedMedia })
      })
    } else {
      const currentScene = scenes.find(s => s.id === mediaPickerSceneId)
      const updatedMedia = updateMediaWithNewAsset(currentScene?.media, media)
      updateScene(mediaPickerSceneId, { media: updatedMedia })
    }

    setMediaPickerSceneId(null)
  }

  // Handle voice select with automatic regeneration
  const handleVoiceSelect = async (
    voice: ElevenVoice,
    applyToAll?: boolean
  ) => {
    if (!voicePickerSceneId) return

    // Store the scene ID before clearing it
    const currentVoicePickerSceneId = voicePickerSceneId

    // Update UI immediately to show the selected voice
    if (applyToAll) {
      scenes.forEach(scene => {
        updateScene(scene.id, {
          voiceSettings: {
            ...scene.voiceSettings,
            voiceName: voice.name,
            voiceId: voice.voice_id,
          },
        })
      })
    } else {
      const scene = scenes.find(s => s.id === currentVoicePickerSceneId)
      if (!scene) return
      updateScene(currentVoicePickerSceneId, {
        voiceSettings: {
          ...scene.voiceSettings,
          voiceName: voice.name,
          voiceId: voice.voice_id,
        },
      })
    }

    // Close modal
    setVoicePickerSceneId(null)

    // Trigger automatic voice regeneration after UI update
    setTimeout(async () => {
      try {
        if (applyToAll) {
          // Show toast for bulk regeneration and track each scene
          const scenesToRegenerate = scenes.filter(scene => scene.text)

          // Set all scenes as generating
          setIsGenerating(prev => {
            const newState = { ...prev }
            scenesToRegenerate.forEach(scene => {
              newState[scene.id] = true
            })
            return newState
          })

          // Use batch generation to save project only once at the end
          const batchTasks = scenesToRegenerate.map(scene => ({
            text: scene.text,
            voice_id: scene.voiceSettings.voiceId,
            userId: session?.user?.id,
          }))

          toast.promise(
            (async () => {
              try {
                const results =
                  await gatedVoiceGeneration.generateVoiceBatch(batchTasks)

                // Update scenes with generated voices
                for (let i = 0; i < results.length; i++) {
                  const result = results[i]
                  const scene = scenesToRegenerate[i]

                  if (result?.audioUrl) {
                    const audio = new Audio(result.audioUrl)
                    audio.onloadedmetadata = () => {
                      updateScene(scene.id, {
                        voiceSettings: {
                          ...scene.voiceSettings,
                          voiceUrl: result.audioUrl,
                        },
                        duration: audio.duration,
                        voiceover: {
                          audioUrl: result.audioUrl,
                          audioDuration: audio.duration,
                          volume: scene.voiceSettings.voiceVol * 100,
                          speed: scene.voiceSettings.voiceSpeed,
                        },
                      })
                    }
                  }
                }

                return results
              } finally {
                // Clear generating state for all scenes
                setIsGenerating(prev => {
                  const newState = { ...prev }
                  scenesToRegenerate.forEach(scene => {
                    newState[scene.id] = false
                  })
                  return newState
                })
              }
            })(),
            {
              loading: `Regenerating voices for ${scenesToRegenerate.length} scenes...`,
              success: 'All voices regenerated successfully!',
              error: 'Some voices failed to regenerate',
            }
          )
        } else {
          // Regenerate single scene
          const scene = scenes.find(s => s.id === currentVoicePickerSceneId)
          if (scene && scene.text) {
            await handleGenerateVoiceover(currentVoicePickerSceneId)
          }
        }
      } catch (error) {
        console.error('Auto-regeneration failed:', error)
        toast.error('Voice regeneration failed, but voice selection was saved')
      }
    }, 100) // Small delay to allow UI to update
  }

  const handleRemoveMedia = (sceneId: string) => {
    updateScene(sceneId, { media: undefined })
  }

  const handlePreviewMedia = (media: VideoMedia | null | undefined) => {
    if (!media) return
    setPreviewMedia({
      url: media.url,
      type: media.type,
    })
  }

  const handleGenerateVoiceover = async (sceneId: string) => {
    const scene = scenes.find(s => s.id === sceneId)
    if (!scene || !scene.voiceSettings.voiceId || !scene.text) return

    // Set generating state for this specific scene
    setIsGenerating(prev => ({ ...prev, [sceneId]: true }))

    try {
      const result = await gatedVoiceGeneration.generateVoice({
        text: scene.text,
        voice_id: scene.voiceSettings.voiceId,
        userId: session?.user?.id, // Pass userId
      })

      // Clear generating state
      setIsGenerating(prev => ({ ...prev, [sceneId]: false }))

      // If result is null, it means the limit was reached and upgrade modal was shown
      if (!result) return

      if (result.audioUrl) {
        const audio = new Audio(result.audioUrl)
        audio.onloadedmetadata = () => {
          // Generate captions using the centralized function
          import('@/lib/remotion/utils/captionUtils').then(
            ({ generateCaptionsFromElevenLabs, generateBasicCaptions }) => {
              const captions = result.alignment
                ? generateCaptionsFromElevenLabs(
                    scene.text,
                    result.alignment,
                    audio.duration
                  )
                : generateBasicCaptions(scene.text, audio.duration)

              updateScene(sceneId, {
                voiceSettings: {
                  ...scene.voiceSettings,
                  voiceUrl: result.audioUrl, // Use voiceUrl to match existing schema
                  alignment: result.alignment, // Save alignment data for future use
                },
                // Update voiceover field to ensure new audio is used (has higher priority in Remotion)
                voiceover: {
                  audioUrl: result.audioUrl,
                  audioDuration: audio.duration,
                  volume: scene.voiceSettings.voiceVol || 100,
                  speed: scene.voiceSettings.voiceSpeed || 1,
                },
                duration: audio.duration,
                captions: captions, // Save generated captions
              })

              // Force Remotion player to refresh by seeking to current time
              setTimeout(() => {
                const currentPlayer =
                  useVideoStore.getState().playerRef?.current
                if (currentPlayer) {
                  const currentFrame = currentPlayer.getCurrentFrame() || 0
                  currentPlayer.seekTo(currentFrame)
                }
              }, 100)
            }
          )
        }

        audio.onerror = () => {
          // Even on audio error, try to generate captions if we have alignment
          import('@/lib/remotion/utils/captionUtils').then(
            ({ generateCaptionsFromElevenLabs, generateBasicCaptions }) => {
              const captions = result.alignment
                ? generateCaptionsFromElevenLabs(
                    scene.text,
                    result.alignment,
                    5
                  ) // fallback duration
                : generateBasicCaptions(scene.text, 5)

              updateScene(sceneId, {
                voiceSettings: {
                  ...scene.voiceSettings,
                  voiceUrl: result.audioUrl, // Use voiceUrl to match existing schema
                  alignment: result.alignment,
                },
                // Update voiceover field to ensure new audio is used (has higher priority in Remotion)
                voiceover: {
                  audioUrl: result.audioUrl,
                  audioDuration: 5, // fallback duration
                  volume: scene.voiceSettings.voiceVol || 100,
                  speed: scene.voiceSettings.voiceSpeed || 1,
                },
                captions: captions,
              })

              // Force Remotion player to refresh by seeking to current time
              setTimeout(() => {
                const currentPlayer =
                  useVideoStore.getState().playerRef?.current
                if (currentPlayer) {
                  const currentFrame = currentPlayer.getCurrentFrame() || 0
                  currentPlayer.seekTo(currentFrame)
                }
              }, 100)
            }
          )
        }
      }
    } catch (error) {
      console.error('Failed to generate voiceover:', error)
      // Clear generating state on error
      setIsGenerating(prev => ({ ...prev, [sceneId]: false }))
    }
  }

  const handlePlayPause = (sceneId: string) => {
    const scene = scenes.find(s => s.id === sceneId)
    if (!scene?.voiceSettings.voiceUrl) return

    if (isPlaying[sceneId]) {
      const audio = audioRefs.current[sceneId]
      if (audio) {
        audio.pause()
        setIsPlaying(prev => ({ ...prev, [sceneId]: false }))
      }
    } else {
      if (!audioRefs.current[sceneId]) {
        audioRefs.current[sceneId] = new Audio(scene.voiceSettings.voiceUrl)
      }

      const audio = audioRefs.current[sceneId]
      if (audio) {
        audio.play()
        audio.onended = () =>
          setIsPlaying(prev => ({ ...prev, [sceneId]: false }))
        audio.onpause = () =>
          setIsPlaying(prev => ({ ...prev, [sceneId]: false }))
      }
      setIsPlaying(prev => ({ ...prev, [sceneId]: true }))
    }
  }

  const handleMusicPlayPause = async () => {
    if (!selectedMusic?.previewUrl) return

    try {
      if (isMusicPlaying) {
        // Pause the currently playing music
        musicGlobalAudio.pause()
        setIsMusicPlaying(false)
      } else {
        // Play the music using global audio manager
        await musicGlobalAudio.play(selectedMusic.previewUrl, {
          volume: musicVolume / 100,
          onPlay: () => {
            setIsMusicPlaying(true)
          },
          onEnded: () => {
            setIsMusicPlaying(false)
          },
          onPause: () => {
            setIsMusicPlaying(false)
          },
          onError: error => {
            console.error('Error playing music:', error)
            setIsMusicPlaying(false)
          },
        })
      }
    } catch (error) {
      console.error('Error in handleMusicPlayPause:', error)
      setIsMusicPlaying(false)
    }
  }

  // Update music volume when it changes
  useEffect(() => {
    musicGlobalAudio.setVolume(musicVolume / 100)
  }, [musicVolume, musicGlobalAudio])

  // Utility functions for mm:ss.SS <-> seconds
  function secondsToMmssSS(seconds: number) {
    if (!isFinite(seconds) || seconds < 0) {
      return '0:00.00'
    }
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    const ms = Math.round((seconds - Math.floor(seconds)) * 100)
    return `${mins}:${secs.toString().padStart(2, '0')}.${ms.toString().padStart(2, '0')}`
  }
  function mmssSSToSeconds(mmss: string) {
    if (!mmss || typeof mmss !== 'string') {
      return 0
    }
    const [minSec, ms = '0'] = mmss.split('.')
    const [mins, secs] = minSec.split(':').map(Number)
    if (isNaN(mins) || isNaN(secs)) {
      return 0
    }
    return mins * 60 + secs + parseInt(ms, 10) / 100
  }
  return (
    <div className='w-full h-full overflow-y-auto bg-background'>
      <Tabs
        value={tab}
        onValueChange={value => setTab(value as 'scenes' | 'settings')}
        className='w-full h-full flex flex-col'
      >
        {/* Sticky Tabs Header */}
        <div className='sticky top-0 z-10 bg-background border-b'>
          <div className='flex items-center gap-2 p-1.5'>
            <TabsList className='flex-1 h-9 p-0.5 bg-muted/30 rounded-lg border-0'>
              <TabsTrigger
                value='scenes'
                className='flex-1 h-8 flex items-center justify-center gap-1 rounded-md data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm font-medium text-xs px-2'
              >
                <List className='h-3.5 w-3.5' />
                <span>Scenes</span>
              </TabsTrigger>
              <TabsTrigger
                value='settings'
                className='flex-1 h-8 flex items-center justify-center gap-1 rounded-md data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm font-medium text-xs px-2'
              >
                <Settings className='h-3.5 w-3.5' />
                <span>Settings</span>
              </TabsTrigger>
            </TabsList>

            {/* Auto Pick Assets Button */}
            {/* <button
              className='h-9 px-3 rounded-lg bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90 text-primary-foreground font-medium text-xs flex items-center gap-1.5 transition-all duration-200 shadow-sm hover:shadow-md whitespace-nowrap'
              title='Auto pick assets with AI'
            >
              <Sparkles className='h-3.5 w-3.5' />
              <span className='hidden sm:inline'>Auto pick assets</span>
              <span className='sm:hidden'>AI</span>
            </button> */}
          </div>
        </div>

        <TabsContent value='scenes' className='p-4 flex-1 mt-0'>
          <div className='flex flex-col gap-6'>
            {/* Background Audio Settings (Speech) - only show if project.speech is not null */}
            {project?.speech ? (
              <div className='rounded-lg border bg-card p-3 space-y-3'>
                <div className='flex items-center justify-between'>
                  <div className='font-medium text-sm flex items-center gap-2'>
                    <Mic className='h-4 w-4' />
                    Speech
                  </div>
                  {/* <div className='flex items-center gap-2'>
                    <Checkbox
                      id='disable-speech'
                      checked={!speechEnabled}
                      onCheckedChange={checked =>
                        typeof checked === 'boolean' &&
                        setSpeechEnabled(!checked)
                      }
                    />
                    <label
                      htmlFor='disable-speech'
                      className='text-xs text-muted-foreground select-none'
                    >
                      Disable speech
                    </label>
                  </div> */}
                </div>

                {/* Responsive Layout */}
                <div className='space-y-3'>
                  <div className='flex items-center gap-3'>
                    <div className='h-7 px-2 text-xs gap-1 flex-1 min-w-0 hover:bg-transparent flex items-center justify-center border rounded-md border-input text-foreground'>
                      <span className='w-2 h-2 bg-primary rounded-full flex-shrink-0'></span>
                      <span className='truncate max-w-[80px] md:max-w-[100px] lg:max-w-[120px] xl:max-w-[140px] 2xl:max-w-[160px]'>
                        {selectedSpeech?.name || 'Speech Track'}
                      </span>
                    </div>

                    {/* {selectedSpeech?.src && (
                      <Button
                        variant='ghost'
                        size='sm'
                        className='h-7 w-7 p-0 flex-shrink-0'
                        onClick={() => {
                          if (!speechAudioRef.current) {
                            speechAudioRef.current = new Audio(
                              selectedSpeech.src
                            )
                            speechAudioRef.current.volume = speechVolume / 100
                          }
                          if (isSpeechPlaying) {
                            speechAudioRef.current.pause()
                            setIsSpeechPlaying(false)
                          } else {
                            speechAudioRef.current.play()
                            setIsSpeechPlaying(true)
                            speechAudioRef.current.onended = () =>
                              setIsSpeechPlaying(false)
                            speechAudioRef.current.onpause = () =>
                              setIsSpeechPlaying(false)
                          }
                        }}
                        title={isSpeechPlaying ? 'Pause speech' : 'Play speech'}
                      >
                        {isSpeechPlaying ? (
                          <Pause className='w-3 h-3' />
                        ) : (
                          <Play className='w-3 h-3' />
                        )}
                      </Button>
                    )} */}
                  </div>

                  <div className='flex items-center gap-3'>
                    <Volume2 className='h-3 w-3 text-muted-foreground flex-shrink-0' />
                    <div className='flex-1'>
                      <Slider
                        value={[speechVolume]}
                        onValueChange={([value]) => {
                          setSpeechVolume(value)
                          if (speechAudioRef.current) {
                            speechAudioRef.current.volume = value / 100
                          }
                        }}
                        max={100}
                        min={0}
                        step={1}
                        className='w-full'
                      />
                    </div>
                    <span className='w-8 text-right text-xs text-muted-foreground flex-shrink-0'>
                      {speechVolume}%
                    </span>
                  </div>
                </div>
              </div>
            ) : null}

            <Accordion
              type='single'
              collapsible
              value={currentScene}
              onValueChange={value => {
                if (value) {
                  handleSceneSelect(value)
                }
              }}
              className='space-y-2 md:space-y-3'
            >
              {scenes.map((scene, index) => {
                // Determine if this is a podcast/audio flow
                const isPodcastOrAudioFlow = !!project?.speech

                const { startInput, endInput, endError, showEndError } =
                  sceneInputs[scene.id] || {}

                return (
                  <div key={scene.id}>
                    <AccordionItem
                      value={scene.id}
                      className={cn(
                        'rounded-lg bg-card transition-all duration-200 overflow-hidden shadow-sm',
                        !isMobile && currentScene === scene.id
                          ? 'ring-2 ring-primary border-primary/50'
                          : 'border-border'
                      )}
                    >
                      <AccordionTrigger className='px-0 py-0 border-b bg-muted/40 hover:no-underline [&>svg]:hidden'>
                        <div
                          className='w-full'
                          onClick={e => {
                            e.stopPropagation()
                            handleSceneSelect(scene.id)
                          }}
                        >
                          {/* Header Row */}
                          <SceneHeader
                            scene={scene}
                            sceneIndex={index}
                            isGenerating={isGenerating[scene.id] || false}
                            isPlaying={isPlaying[scene.id] || false}
                            onVoicePickerClick={() =>
                              handleVoicePicker(scene.id)
                            }
                            onGenerateVoiceover={() =>
                              handleGenerateVoiceover(scene.id)
                            }
                            onPlayPause={() => handlePlayPause(scene.id)}
                            formatDuration={formatDuration}
                            hideVoicePicker={isPodcastOrAudioFlow}
                            isPodcastOrAudioFlow={isPodcastOrAudioFlow}
                            canRegenerate={voiceRegeneration.allowed}
                          />

                          {/* Collapsed Preview Row - Only show when not expanded */}
                          {currentScene !== scene.id && (
                            <div className='flex items-center gap-3 md:gap-4 px-4 md:px-5 pb-3 md:pb-4'>
                              <SceneMediaThumbnail
                                media={scene.media || undefined}
                                size='small'
                                sceneIndex={index}
                                onChangeMedia={() =>
                                  handleMediaPicker(scene.id)
                                }
                                showAutoPickHint={true}
                              />
                              <div className='flex-1 max-w-[150px] md:max-w-[200px] lg:max-w-[250px] xl:max-w-[300px] 2xl:max-w-[350px]'>
                                {isPodcastOrAudioFlow ? (
                                  <div className='flex gap-4 items-center text-xs text-muted-foreground'>
                                    <span>
                                      <span className='font-medium'>
                                        Start:
                                      </span>{' '}
                                      {startInput}
                                    </span>
                                    <span>
                                      <span className='font-medium'>End:</span>{' '}
                                      {endInput}
                                    </span>
                                  </div>
                                ) : (
                                  <p className='text-sm text-muted-foreground overflow-hidden text-ellipsis whitespace-nowrap max-w-[120px] md:max-w-[180px] lg:max-w-[220px] xl:max-w-[260px] 2xl:max-w-[300px]'>
                                    {scene.text ||
                                      'No voiceover text added yet...'}
                                  </p>
                                )}
                              </div>
                            </div>
                          )}
                        </div>
                      </AccordionTrigger>

                      <AccordionContent className='px-0 pb-0'>
                        {/* Middle Row */}
                        <div className='flex gap-4 md:gap-5 px-4 md:px-5 py-4 md:py-5 items-start'>
                          {isPodcastOrAudioFlow ? (
                            <div className='flex-1'>
                              <div className='space-y-4'>
                                {/* Start Offset and Duration in one row */}
                                <div className='flex gap-2 items-end'>
                                  <SceneMediaThumbnail
                                    media={scene.media || undefined}
                                    size='medium'
                                    sceneIndex={index}
                                    onChangeMedia={() =>
                                      handleMediaPicker(scene.id)
                                    }
                                    onPreviewMedia={
                                      scene.media
                                        ? handlePreviewMedia
                                        : undefined
                                    }
                                    onRemoveMedia={() =>
                                      handleRemoveMedia(scene.id)
                                    }
                                    showAutoPickHint={true}
                                  />
                                  <div>
                                    <label className='text-xs font-medium text-muted-foreground mb-2 flex items-center gap-1'>
                                      Start
                                      <Tooltip>
                                        <TooltipTrigger asChild>
                                          <span className='cursor-help text-muted-foreground'>
                                            <svg
                                              width='14'
                                              height='14'
                                              fill='none'
                                              viewBox='0 0 24 24'
                                            >
                                              <circle
                                                cx='12'
                                                cy='12'
                                                r='10'
                                                stroke='currentColor'
                                                strokeWidth='2'
                                              />
                                              <text
                                                x='12'
                                                y='16'
                                                textAnchor='middle'
                                                fontSize='12'
                                                fill='currentColor'
                                              >
                                                ?
                                              </text>
                                            </svg>
                                          </span>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                          Format: mm:ss.SS (min,sec,millisecond)
                                        </TooltipContent>
                                      </Tooltip>
                                    </label>
                                    <input
                                      type='text'
                                      pattern='^\d{1,2}:\d{2}(\.\d{1,2})?$'
                                      value={startInput ?? ''}
                                      onChange={e => {
                                        updateSceneInput(scene.id, {
                                          startInput: e.target.value,
                                        })
                                      }}
                                      onBlur={e => {
                                        const value = e.target.value
                                        if (
                                          /^\d{1,2}:\d{2}(\.\d{1,2})?$/.test(
                                            value
                                          )
                                        ) {
                                          const newStart =
                                            mmssSSToSeconds(value)
                                          updateScene(scene.id, {
                                            startOffset: newStart,
                                          })
                                          if (index > 0) {
                                            const prevScene = scenes[index - 1]
                                            const newPrevDuration =
                                              newStart -
                                              (prevScene.startOffset || 0)
                                            updateScene(prevScene.id, {
                                              duration: newPrevDuration,
                                            })
                                          }
                                        }
                                      }}
                                      className='w-24 h-10 text-sm border border-input rounded-md px-2 bg-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2'
                                      placeholder='0:00.00'
                                      onClick={e => e.stopPropagation()}
                                    />
                                  </div>
                                  <div>
                                    <label className='text-xs font-medium text-muted-foreground mb-2 flex items-center gap-1'>
                                      End
                                      <Tooltip>
                                        <TooltipTrigger asChild>
                                          <span className='cursor-help text-muted-foreground'>
                                            <svg
                                              width='14'
                                              height='14'
                                              fill='none'
                                              viewBox='0 0 24 24'
                                            >
                                              <circle
                                                cx='12'
                                                cy='12'
                                                r='10'
                                                stroke='currentColor'
                                                strokeWidth='2'
                                              />
                                              <text
                                                x='12'
                                                y='16'
                                                textAnchor='middle'
                                                fontSize='12'
                                                fill='currentColor'
                                              >
                                                ?
                                              </text>
                                            </svg>
                                          </span>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                          Format: mm:ss.SS (min,sec,millisecond)
                                        </TooltipContent>
                                      </Tooltip>
                                    </label>
                                    <input
                                      type='text'
                                      pattern='^\d{1,2}:\d{2}(\.\d{1,2})?$'
                                      value={endInput ?? ''}
                                      onChange={e => {
                                        updateSceneInput(scene.id, {
                                          endInput: e.target.value,
                                        })
                                      }}
                                      onBlur={e => {
                                        const value = e.target.value
                                        if (
                                          /^\d{1,2}:\d{2}(\.\d{1,2})?$/.test(
                                            value
                                          )
                                        ) {
                                          const newEnd = mmssSSToSeconds(value)
                                          const start = scene.startOffset || 0
                                          let nextSceneEnd = Infinity
                                          if (index < scenes.length - 1) {
                                            const nextScene = scenes[index + 1]
                                            nextSceneEnd =
                                              (nextScene.startOffset || 0) +
                                              (nextScene.duration || 0)
                                          }
                                          if (newEnd <= start) {
                                            updateSceneInput(scene.id, {
                                              endInput: secondsToMmssSS(
                                                start + (scene.duration || 0)
                                              ),
                                              endError:
                                                'End time must be greater than start time.',
                                              showEndError: true,
                                            })
                                            return
                                          }
                                          if (newEnd > nextSceneEnd - 0.5) {
                                            updateSceneInput(scene.id, {
                                              endInput: secondsToMmssSS(
                                                start + (scene.duration || 0)
                                              ),
                                              endError:
                                                "End time must be at least 0.5 second less than the next scene's end.",
                                              showEndError: true,
                                            })
                                            return
                                          }
                                          const newDuration = newEnd - start
                                          updateScene(scene.id, {
                                            duration: newDuration,
                                          })
                                          if (index < scenes.length - 1) {
                                            const nextScene = scenes[index + 1]
                                            updateScene(nextScene.id, {
                                              startOffset: newEnd,
                                            })
                                          }
                                        }
                                      }}
                                      className={`w-24 h-10 text-sm border ${endError && showEndError ? 'border-red-500' : 'border-input'} rounded-md px-2 bg-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2`}
                                      placeholder='0:00.00'
                                      onClick={e => e.stopPropagation()}
                                    />
                                  </div>
                                </div>
                                {/* Error message below both inputs */}
                                {showEndError && endError && (
                                  <div className='w-full flex justify-start mt-1'>
                                    <div className='bg-red-50 border border-red-200 text-red-600 text-xs rounded p-2 flex items-center gap-2 max-w-xs'>
                                      <span>{endError}</span>
                                      <button
                                        className='ml-auto px-2 py-0.5 bg-red-100 border border-red-300 rounded text-xs hover:bg-red-200'
                                        onClick={() => {
                                          updateSceneInput(scene.id, {
                                            endError: null,
                                            showEndError: false,
                                          })
                                        }}
                                      >
                                        Ok
                                      </button>
                                    </div>
                                  </div>
                                )}
                                {/* Background Fit, Transition, Animation */}
                                <SceneControls
                                  scene={scene}
                                  onUpdateScene={updates =>
                                    updateScene(scene.id, updates)
                                  }
                                  hideAudioControls={true}
                                />
                                <SceneEffects
                                  scene={scene}
                                  onUpdateScene={updates =>
                                    updateScene(scene.id, updates)
                                  }
                                />
                              </div>
                            </div>
                          ) : (
                            <>
                              {/* Voiceover text input */}
                              <div className='flex-1'>
                                <div className='relative flex gap-2'>
                                  <SceneMediaThumbnail
                                    media={scene.media || undefined}
                                    size='medium'
                                    sceneIndex={index}
                                    onChangeMedia={() =>
                                      handleMediaPicker(scene.id)
                                    }
                                    onPreviewMedia={
                                      scene.media
                                        ? handlePreviewMedia
                                        : undefined
                                    }
                                    onRemoveMedia={() =>
                                      handleRemoveMedia(scene.id)
                                    }
                                    showAutoPickHint={true}
                                  />
                                  <textarea
                                    value={scene.text || ''}
                                    placeholder='Enter the voiceover text for this scene...'
                                    className={cn(
                                      'w-full min-h-[120px] text-sm border rounded p-3 pr-12 resize-none',
                                      scene.text && scene.text.length > 200
                                        ? 'ring-2 ring-red-500 border-red-500'
                                        : ''
                                    )}
                                    onChange={e => {
                                      const newText = e.target.value
                                      updateScene(scene.id, {
                                        text: newText,
                                      })
                                    }}
                                    onClick={e => e.stopPropagation()}
                                    autoComplete='off'
                                    maxLength={1000}
                                  />

                                  {/* AI Assistant */}
                                  <AIAssistant
                                    sceneId={scene.id}
                                    isOpen={
                                      aiAssistant.aiDropdownOpen[scene.id] ||
                                      false
                                    }
                                    onOpenChange={open =>
                                      aiAssistant.handleAiDropdownChange(
                                        scene.id,
                                        open
                                      )
                                    }
                                    input={aiAssistant.aiInputs[scene.id] || ''}
                                    onInputChange={value =>
                                      aiAssistant.handleAiInputChange(
                                        scene.id,
                                        value
                                      )
                                    }
                                    isLoading={
                                      aiAssistant.aiLoading[scene.id] || false
                                    }
                                    response={
                                      aiAssistant.aiResponses[scene.id] || ''
                                    }
                                    showResponse={
                                      aiAssistant.showAiResponse[scene.id] ||
                                      false
                                    }
                                    onAction={action =>
                                      aiAssistant.handleAiAction(
                                        scene.id,
                                        scene,
                                        action
                                      )
                                    }
                                    onSubmit={() =>
                                      aiAssistant.handleAiSubmit(
                                        scene.id,
                                        scene
                                      )
                                    }
                                    onInsertResponse={() =>
                                      aiAssistant.handleInsertAiResponse(
                                        scene.id
                                      )
                                    }
                                    onDiscardResponse={() =>
                                      aiAssistant.handleDiscardAiResponse(
                                        scene.id
                                      )
                                    }
                                    onTryAgain={() =>
                                      aiAssistant.handleTryAgain(
                                        scene.id,
                                        scene
                                      )
                                    }
                                  />
                                </div>
                                <div className='flex justify-between text-xs mt-1'>
                                  {scene.text && scene.text.length > 200 ? (
                                    <>
                                      <span className='text-xs text-red-500'>
                                        Voiceover text cannot exceed 200
                                        characters.
                                      </span>
                                      <span className='ml-auto text-muted-foreground'>
                                        {scene.text.length}/200 characters
                                      </span>
                                    </>
                                  ) : (
                                    <span className='ml-auto text-muted-foreground'>
                                      {scene.text?.length || 0}/200 characters
                                    </span>
                                  )}
                                </div>

                                {/* Scene Controls */}
                                <SceneControls
                                  scene={scene}
                                  onUpdateScene={updates =>
                                    updateScene(scene.id, updates)
                                  }
                                />

                                {/* Scene Effects */}
                                <SceneEffects
                                  scene={scene}
                                  onUpdateScene={updates =>
                                    updateScene(scene.id, updates)
                                  }
                                />
                              </div>
                            </>
                          )}
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    {/* Scene Actions */}
                    <SceneActions
                      sceneIndex={index}
                      totalScenes={scenes.length}
                      onAddScene={() => handleAddScene(index)}
                      onDeleteScene={() => handleDeleteScene(scene.id)}
                      onMoveUp={() => handleMoveUp(index)}
                      onMoveDown={() => handleMoveDown(index)}
                    />
                  </div>
                )
              })}
            </Accordion>
          </div>
        </TabsContent>

        <TabsContent value='settings' className='p-4 flex-1 mt-0'>
          <div className='flex flex-col gap-6'>
            {/* Compact Music Settings */}
            <div className='rounded-lg border bg-card p-3 space-y-3'>
              <div className='flex items-center justify-between'>
                <div className='font-medium text-sm flex items-center gap-2'>
                  <Music2 className='h-4 w-4' />
                  Music
                </div>
                <div className='flex items-center gap-2'>
                  <Checkbox
                    id='disable-music'
                    checked={!musicEnabled}
                    onCheckedChange={checked =>
                      typeof checked === 'boolean' && setMusicEnabled(!checked)
                    }
                  />
                  <label
                    htmlFor='disable-music'
                    className='text-xs text-muted-foreground select-none'
                  >
                    Disable music
                  </label>
                </div>
              </div>

              {/* Responsive Layout */}
              <div className='space-y-3'>
                {/* Music Track and Play Button Row */}
                <div className='flex items-center gap-3'>
                  <Button
                    variant='outline'
                    size='sm'
                    className='h-7 px-2 text-xs gap-1 flex-1 min-w-0'
                    onClick={() => setMusicPickerOpen(true)}
                  >
                    <span className='w-2 h-2 bg-primary rounded-full flex-shrink-0'></span>
                    <span className='truncate max-w-[100px] md:max-w-[120px] lg:max-w-[140px] xl:max-w-[160px] 2xl:max-w-[180px]'>
                      {selectedMusic?.title ||
                        'Happy to Be Happy - Instrumental Version'}
                    </span>
                  </Button>

                  {/* Play/Pause Button */}
                  {selectedMusic?.previewUrl && (
                    <Button
                      variant='ghost'
                      size='sm'
                      className='h-7 w-7 p-0 flex-shrink-0'
                      onClick={handleMusicPlayPause}
                      title={isMusicPlaying ? 'Pause music' : 'Play music'}
                    >
                      {isMusicPlaying ? (
                        <Pause className='w-3 h-3' />
                      ) : (
                        <Play className='w-3 h-3' />
                      )}
                    </Button>
                  )}
                </div>

                {/* Volume Control - Full Width on Small Screens */}
                <div className='flex items-center gap-3'>
                  <Volume2 className='h-3 w-3 text-muted-foreground flex-shrink-0' />
                  <div className='flex-1'>
                    <Slider
                      value={[musicVolume]}
                      onValueChange={([value]) => setMusicVolume(value)}
                      max={100}
                      min={0}
                      step={1}
                      className='w-full'
                    />
                  </div>
                  <span className='w-8 text-right text-xs text-muted-foreground flex-shrink-0'>
                    {musicVolume}%
                  </span>
                </div>
              </div>
            </div>
            {/* Caption Section */}
            <CaptionSettings />
          </div>
        </TabsContent>
      </Tabs>

      {/* Modals */}
      <MediaPickerModal
        open={!!mediaPickerSceneId}
        onClose={() => setMediaPickerSceneId(null)}
        onSelect={handleMediaSelect}
        project={{ importedMedia: project?.importedMedia }}
      />
      <VoicePickerModal
        isOpen={!!voicePickerSceneId}
        onClose={() => setVoicePickerSceneId(null)}
        onSelectVoice={handleVoiceSelect}
      />
      <MusicPickerModal
        isOpen={musicPickerOpen}
        onClose={() => setMusicPickerOpen(false)}
        onSelectMusic={track => {
          setSelectedMusic(track)
          setMusicPickerOpen(false)
        }}
      />

      {/* Media Preview Modal */}
      {previewMedia && (
        <Dialog
          open={!!previewMedia}
          onOpenChange={() => setPreviewMedia(null)}
        >
          <DialogContent className='bg-transparent rounded-lg shadow-lg border-0 w-full max-w-2xl flex flex-col items-center justify-center'>
            <VisuallyHidden>
              <DialogTitle>Media Preview</DialogTitle>
            </VisuallyHidden>
            <div className='relative w-full h-max-[20rem] bg-black rounded-lg overflow-hidden'>
              {previewMedia.type === 'video' ? (
                <video
                  src={previewMedia.url}
                  controls
                  className='w-full h-full object-contain'
                  autoPlay
                  muted
                />
              ) : (
                <img
                  src={previewMedia.url}
                  alt='Preview'
                  className='w-full h-full object-contain'
                />
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}
